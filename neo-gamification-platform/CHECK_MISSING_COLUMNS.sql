-- Check which columns exist in stage_impacts table
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'stage_impacts' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if views exist
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('features_with_impacts', 'features_complete');

-- Check if indexes exist
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'stage_impacts' 
AND schemaname = 'public';
