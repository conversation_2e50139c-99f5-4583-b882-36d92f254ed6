-- Safe migration script - only adds missing columns and recreates views
-- Run this instead of the full migration

-- Add missing columns only if they don't exist
DO $$ 
BEGIN
    -- Add relationship_strength if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'stage_impacts' 
                   AND column_name = 'relationship_strength') THEN
        ALTER TABLE stage_impacts 
        ADD COLUMN relationship_strength VARCHAR(20) DEFAULT 'weak' 
        CHECK (relationship_strength IN ('weak', 'moderate', 'strong'));
    END IF;

    -- Add estimated_improvement if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'stage_impacts' 
                   AND column_name = 'estimated_improvement') THEN
        ALTER TABLE stage_impacts 
        ADD COLUMN estimated_improvement VARCHAR(50);
    END IF;

    -- Add analysis_timestamp if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'stage_impacts' 
                   AND column_name = 'analysis_timestamp') THEN
        ALTER TABLE stage_impacts 
        ADD COLUMN analysis_timestamp TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add model_used if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'stage_impacts' 
                   AND column_name = 'model_used') THEN
        ALTER TABLE stage_impacts 
        ADD COLUMN model_used VARCHAR(100);
    END IF;
END $$;

-- Add comments for new columns
COMMENT ON COLUMN stage_impacts.relationship_strength IS 'Strength of relationship between feature and stage (weak, moderate, strong)';
COMMENT ON COLUMN stage_impacts.estimated_improvement IS 'Estimated percentage improvement if impact is medium/high';
COMMENT ON COLUMN stage_impacts.analysis_timestamp IS 'When the AI analysis was performed';
COMMENT ON COLUMN stage_impacts.model_used IS 'AI model used for the analysis';

-- Create indexes for new columns (if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_stage_impacts_relationship') THEN
        CREATE INDEX idx_stage_impacts_relationship ON stage_impacts(relationship_strength);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_stage_impacts_analysis_timestamp') THEN
        CREATE INDEX idx_stage_impacts_analysis_timestamp ON stage_impacts(analysis_timestamp);
    END IF;
END $$;

-- Recreate views with new fields (always safe to recreate)
DROP VIEW IF EXISTS features_with_impacts;
DROP VIEW IF EXISTS features_complete;

-- Recreate features_with_impacts view
CREATE VIEW features_with_impacts AS
SELECT 
    f.*,
    json_agg(
        json_build_object(
            'stage', si.stage,
            'impact_level', si.impact_level,
            'reasoning', si.reasoning,
            'confidence_score', si.confidence_score,
            'relationship_strength', si.relationship_strength,
            'estimated_improvement', si.estimated_improvement,
            'analysis_timestamp', si.analysis_timestamp,
            'model_used', si.model_used
        )
    ) as stage_impacts
FROM features f
LEFT JOIN stage_impacts si ON f.id = si.feature_id
GROUP BY f.id, f.feature_id, f.name, f.description, f.category, f.stage, 
         f.priority, f.difficulty, f.research_backed, f.justification, 
         f.created_at, f.updated_at;

-- Recreate features_complete view with enhanced stage impacts
CREATE VIEW features_complete AS
SELECT 
    f.*,
    (SELECT json_agg(
        json_build_object(
            'stage', si.stage,
            'impact_level', si.impact_level,
            'reasoning', si.reasoning,
            'confidence_score', si.confidence_score,
            'relationship_strength', si.relationship_strength,
            'estimated_improvement', si.estimated_improvement,
            'analysis_timestamp', si.analysis_timestamp,
            'model_used', si.model_used
        )
    ) FROM stage_impacts si WHERE si.feature_id = f.id) as stage_impacts,
    (SELECT json_agg(
        json_build_object(
            'company_name', fe.company_name,
            'implementation_description', fe.implementation_description,
            'success_story', fe.success_story,
            'campaign_name', fe.campaign_name,
            'currency_name', fe.currency_name,
            'duration', fe.duration,
            'frequency', fe.frequency,
            'metrics_before', fe.metrics_before,
            'metrics_after', fe.metrics_after,
            'source_url', fe.source_url
        )
    ) FROM feature_examples fe WHERE fe.feature_id = f.id) as feature_examples,
    (SELECT array_agg(fm.mechanic_name) FROM feature_mechanics fm WHERE fm.feature_id = f.id) as mechanics,
    (SELECT array_agg(fi.impact_area) FROM feature_impacts fi WHERE fi.feature_id = f.id) as impact_areas,
    -- Calculate business value score based on AI analysis
    COALESCE((
        SELECT AVG(
            CASE si.impact_level
                WHEN 'high' THEN 3
                WHEN 'medium' THEN 2
                WHEN 'low' THEN 1
                ELSE 0
            END * (COALESCE(si.confidence_score, 0) / 100.0)
        )
        FROM stage_impacts si 
        WHERE si.feature_id = f.id
    ), 0) as business_value_score,
    -- Calculate technical complexity score (inverse of difficulty)
    CASE f.difficulty
        WHEN 'low' THEN 3
        WHEN 'medium' THEN 2
        WHEN 'high' THEN 1
    END as technical_complexity_score,
    -- Calculate user impact score based on stage impacts
    COALESCE((
        SELECT COUNT(*) 
        FROM stage_impacts si 
        WHERE si.feature_id = f.id 
        AND si.impact_level IN ('medium', 'high')
        AND COALESCE(si.confidence_score, 0) >= 50
    ), 0) as user_impact_score,
    -- Estimated effort hours based on difficulty and complexity
    CASE f.difficulty
        WHEN 'low' THEN 40
        WHEN 'medium' THEN 80
        WHEN 'high' THEN 160
    END as estimated_effort_hours
FROM features f;
