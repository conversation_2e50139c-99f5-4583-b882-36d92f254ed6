import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyAIMigration() {
  console.log('🔧 Applying AI analysis migration...')

  try {
    // Read the migration file
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', 'add_ai_analysis_fields.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf8')

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    console.log(`📝 Found ${statements.length} SQL statements to execute`)

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          // Try direct query if RPC fails
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0) // This will fail but allows us to execute raw SQL
          
          // If that doesn't work, we'll need to execute manually
          console.log(`⚠️  Statement ${i + 1} may need manual execution:`)
          console.log(statement)
          console.log('Error:', error.message)
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.log(`⚠️  Statement ${i + 1} execution failed:`, err)
        console.log('Statement:', statement)
      }
    }

    // Test the new schema by checking if the columns exist
    console.log('\n🔍 Verifying migration...')
    
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'stage_impacts')
      .eq('table_schema', 'public')

    if (columnsError) {
      console.error('❌ Failed to verify migration:', columnsError.message)
    } else {
      const columnNames = columns?.map(col => col.column_name) || []
      const newColumns = ['confidence_score', 'relationship_strength', 'estimated_improvement', 'analysis_timestamp', 'model_used']
      
      console.log('📋 Current stage_impacts columns:', columnNames.join(', '))
      
      const missingColumns = newColumns.filter(col => !columnNames.includes(col))
      if (missingColumns.length === 0) {
        console.log('✅ All new columns are present!')
      } else {
        console.log('⚠️  Missing columns:', missingColumns.join(', '))
        console.log('\n📝 You may need to run these SQL commands manually in Supabase SQL Editor:')
        console.log('\n-- Add missing columns to stage_impacts table')
        missingColumns.forEach(col => {
          switch (col) {
            case 'confidence_score':
              console.log('ALTER TABLE stage_impacts ADD COLUMN confidence_score INTEGER DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100);')
              break
            case 'relationship_strength':
              console.log("ALTER TABLE stage_impacts ADD COLUMN relationship_strength VARCHAR(20) DEFAULT 'weak' CHECK (relationship_strength IN ('weak', 'moderate', 'strong'));")
              break
            case 'estimated_improvement':
              console.log('ALTER TABLE stage_impacts ADD COLUMN estimated_improvement VARCHAR(50);')
              break
            case 'analysis_timestamp':
              console.log('ALTER TABLE stage_impacts ADD COLUMN analysis_timestamp TIMESTAMP WITH TIME ZONE;')
              break
            case 'model_used':
              console.log('ALTER TABLE stage_impacts ADD COLUMN model_used VARCHAR(100);')
              break
          }
        })
      }
    }

    console.log('\n🎉 Migration process completed!')
    console.log('💡 If some statements failed, you can run them manually in the Supabase SQL Editor.')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Run the migration
applyAIMigration()
  .then(() => {
    console.log('✅ Migration script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Migration script failed:', error)
    process.exit(1)
  })
