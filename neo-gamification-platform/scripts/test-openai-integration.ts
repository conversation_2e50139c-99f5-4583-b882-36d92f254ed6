import dotenv from 'dotenv'
import { analyzeFeatureImpact, checkOpenAIConnection, type FeatureAnalysisInput } from '../src/lib/openai'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function testOpenAIIntegration() {
  console.log('🧪 Testing OpenAI Integration...\n')

  // Test 1: Check OpenAI connection
  console.log('1️⃣ Testing OpenAI connection...')
  try {
    const isConnected = await checkOpenAIConnection()
    if (isConnected) {
      console.log('✅ OpenAI connection successful')
    } else {
      console.log('❌ OpenAI connection failed')
      return
    }
  } catch (error) {
    console.log('❌ OpenAI connection error:', error)
    return
  }

  // Test 2: Analyze a sample feature
  console.log('\n2️⃣ Testing feature analysis...')
  
  const sampleFeature: FeatureAnalysisInput = {
    id: 'test-feature-001',
    name: 'Daily Login Streak Rewards',
    description: 'Users receive increasing rewards for consecutive daily logins, encouraging habit formation and regular app engagement.',
    category: 'Engagement',
    stage: 'retention',
    priority: 'high',
    difficulty: 'medium',
    research_backed: true,
    justification: 'Research shows that streak mechanics can increase user retention by up to 40% by leveraging loss aversion psychology.',
    examples: [
      {
        company_name: 'Duolingo',
        success_story: 'Duolingo\'s streak feature increased daily active users by 25% and improved 7-day retention from 55% to 70%.',
        metrics_before: '55% 7-day retention',
        metrics_after: '70% 7-day retention'
      }
    ],
    mechanics: ['Streaks', 'Progressive Rewards', 'Loss Aversion'],
    impact_areas: ['Daily Engagement', 'Habit Formation', 'User Retention']
  }

  try {
    console.log('🔍 Analyzing sample feature:', sampleFeature.name)
    const result = await analyzeFeatureImpact(sampleFeature)
    
    console.log('\n📊 Analysis Results:')
    console.log('Feature ID:', result.feature_id)
    console.log('Overall Confidence:', result.overall_confidence + '%')
    console.log('Model Used:', result.model_used)
    console.log('Analysis Timestamp:', result.analysis_timestamp)
    
    console.log('\n📈 Stage Impact Analysis:')
    result.stage_impacts.forEach(impact => {
      console.log(`\n🎯 ${impact.stage.toUpperCase()}:`)
      console.log(`   Impact Level: ${impact.impact_level}`)
      console.log(`   Confidence: ${impact.confidence_score}%`)
      console.log(`   Relationship: ${impact.relationship_strength}`)
      if (impact.estimated_improvement) {
        console.log(`   Estimated Improvement: ${impact.estimated_improvement}`)
      }
      console.log(`   Reasoning: ${impact.reasoning.substring(0, 100)}...`)
    })

    // Validate results
    console.log('\n✅ Validation:')
    const hasAllStages = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
      .every(stage => result.stage_impacts.some(impact => impact.stage === stage))
    
    console.log('- All AARRR stages analyzed:', hasAllStages ? '✅' : '❌')
    console.log('- Confidence scores valid:', result.stage_impacts.every(i => i.confidence_score >= 0 && i.confidence_score <= 100) ? '✅' : '❌')
    console.log('- Impact levels valid:', result.stage_impacts.every(i => ['none', 'low', 'medium', 'high'].includes(i.impact_level)) ? '✅' : '❌')
    console.log('- Relationship strengths valid:', result.stage_impacts.every(i => ['weak', 'moderate', 'strong'].includes(i.relationship_strength)) ? '✅' : '❌')

    // Check if primary stage has highest impact
    const primaryStageImpact = result.stage_impacts.find(i => i.stage === sampleFeature.stage)
    const hasHighPrimaryImpact = primaryStageImpact && ['medium', 'high'].includes(primaryStageImpact.impact_level)
    console.log('- Primary stage has significant impact:', hasHighPrimaryImpact ? '✅' : '❌')

    console.log('\n🎉 OpenAI integration test completed successfully!')

  } catch (error) {
    console.error('❌ Feature analysis failed:', error)
  }
}

// Test 3: Test error handling
async function testErrorHandling() {
  console.log('\n3️⃣ Testing error handling...')
  
  const invalidFeature: FeatureAnalysisInput = {
    id: 'invalid-feature',
    name: '',
    description: '',
    category: '',
    stage: 'retention',
    priority: 'high',
    difficulty: 'medium',
    research_backed: false
  }

  try {
    const result = await analyzeFeatureImpact(invalidFeature)
    console.log('📊 Error handling result:')
    console.log('- Overall confidence:', result.overall_confidence)
    console.log('- Has fallback data:', result.stage_impacts.length === 5 ? '✅' : '❌')
    console.log('- Error handling works:', result.overall_confidence === 0 ? '✅' : '❌')
  } catch (error) {
    console.log('❌ Error handling test failed:', error)
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testOpenAIIntegration()
    await testErrorHandling()
    
    console.log('\n🏁 All tests completed!')
    console.log('\n💡 Next steps:')
    console.log('1. Run the database migration manually in Supabase SQL Editor')
    console.log('2. Test the matrix page AI analysis button')
    console.log('3. Verify that confidence scores and relationship strengths are stored correctly')
    
  } catch (error) {
    console.error('❌ Test suite failed:', error)
    process.exit(1)
  }
}

// Execute tests
runAllTests()
  .then(() => {
    console.log('✅ Test script completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Test script failed:', error)
    process.exit(1)
  })
