'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { ChevronLeft, Star, ExternalLink, Building, TrendingUp, Users, Target } from 'lucide-react'
import Link from 'next/link'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: 'low' | 'medium' | 'high'
  difficulty: 'low' | 'medium' | 'high'
  research_backed: boolean
  justification: string
  created_at: string
  stage_impacts?: Array<{
    stage: string
    impact_level: 'none' | 'low' | 'medium' | 'high'
    reasoning?: string
  }>
  examples?: Array<{
    company_name: string
    implementation_description?: string
    success_story: string
    metrics_before?: string
    metrics_after?: string
  }>
  mechanics?: string[]
  impact_areas?: string[]
}

const STAGE_COLORS = {
  acquisition: 'bg-blue-500',
  activation: 'bg-green-500',
  retention: 'bg-purple-500',
  referral: 'bg-pink-500',
  revenue: 'bg-yellow-500'
}

const STAGE_ICONS = {
  acquisition: Users,
  activation: Target,
  retention: TrendingUp,
  referral: Users,
  revenue: TrendingUp
}

const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-700',
  medium: 'bg-blue-100 text-blue-700',
  high: 'bg-red-100 text-red-700'
}

const DIFFICULTY_COLORS = {
  low: 'bg-green-100 text-green-700',
  medium: 'bg-yellow-100 text-yellow-700',
  high: 'bg-red-100 text-red-700'
}

const IMPACT_COLORS = {
  none: 'bg-gray-100 text-gray-600',
  low: 'bg-yellow-100 text-yellow-800',
  medium: 'bg-orange-100 text-orange-800',
  high: 'bg-red-100 text-red-800'
}

export default function FeatureDetailPage() {
  const params = useParams()
  const [feature, setFeature] = useState<Feature | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      fetchFeature(params.id as string)
    }
  }, [params.id])

  const fetchFeature = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/features/${id}`)
      if (!response.ok) throw new Error('Failed to fetch feature')
      
      const result = await response.json()
      setFeature(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load feature')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neo-cyan mx-auto mb-4"></div>
          <p className="text-gray-600">Loading feature details...</p>
        </div>
      </div>
    )
  }

  if (error || !feature) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error || 'Feature not found'}</p>
          <Link href="/features" className="btn-primary">
            Back to Features
          </Link>
        </div>
      </div>
    )
  }

  const StageIcon = STAGE_ICONS[feature.stage as keyof typeof STAGE_ICONS] || Target

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/features" className="flex items-center text-gray-600 hover:text-gray-900">
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Features
              </Link>
              <div className="ml-6 h-6 w-px bg-gray-300"></div>
              <h1 className="ml-6 text-xl font-semibold text-gray-900">
                Feature Details
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Feature Header */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className={`w-12 h-12 rounded-lg ${STAGE_COLORS[feature.stage as keyof typeof STAGE_COLORS]} flex items-center justify-center`}>
                    <StageIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{feature.name}</h1>
                    <p className="text-gray-600 capitalize">{feature.stage} Stage</p>
                  </div>
                </div>
                {feature.research_backed && (
                  <div className="flex items-center gap-1 text-yellow-600">
                    <Star className="w-5 h-5 fill-current" />
                    <span className="text-sm font-medium">Research Backed</span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${PRIORITY_COLORS[feature.priority]}`}>
                  {feature.priority} priority
                </span>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${DIFFICULTY_COLORS[feature.difficulty]}`}>
                  {feature.difficulty} difficulty
                </span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                  {feature.category}
                </span>
              </div>

              <p className="text-gray-700 leading-relaxed">{feature.description}</p>
            </div>

            {/* Justification */}
            {feature.justification && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Business Justification</h2>
                <p className="text-gray-700 leading-relaxed">{feature.justification}</p>
              </div>
            )}

            {/* Stage Impacts */}
            {feature.stage_impacts && feature.stage_impacts.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">AARRR Stage Impacts</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {feature.stage_impacts.map((impact) => {
                    const StageIcon = STAGE_ICONS[impact.stage as keyof typeof STAGE_ICONS] || Target
                    return (
                      <div key={impact.stage} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <div className={`w-8 h-8 rounded-lg ${STAGE_COLORS[impact.stage as keyof typeof STAGE_COLORS]} flex items-center justify-center`}>
                            <StageIcon className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 capitalize">{impact.stage}</h3>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${IMPACT_COLORS[impact.impact_level]}`}>
                              {impact.impact_level} impact
                            </span>
                          </div>
                        </div>
                        {impact.reasoning && (
                          <p className="text-sm text-gray-600">{impact.reasoning}</p>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Real-World Examples */}
            {feature.examples && feature.examples.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Real-World Examples</h2>
                <div className="space-y-4">
                  {feature.examples.map((example, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Building className="w-5 h-5 text-gray-500" />
                        <h3 className="font-medium text-gray-900">{example.company_name}</h3>
                      </div>
                      
                      {example.implementation_description && (
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Implementation:</h4>
                          <p className="text-sm text-gray-600">{example.implementation_description}</p>
                        </div>
                      )}
                      
                      <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Success Story:</h4>
                        <p className="text-sm text-gray-600">{example.success_story}</p>
                      </div>
                      
                      {(example.metrics_before || example.metrics_after) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pt-3 border-t border-gray-100">
                          {example.metrics_before && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-1">Before:</h4>
                              <p className="text-sm text-gray-600">{example.metrics_before}</p>
                            </div>
                          )}
                          {example.metrics_after && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-1">After:</h4>
                              <p className="text-sm text-gray-600">{example.metrics_after}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Info */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Info</h3>
              <dl className="space-y-3">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Feature ID</dt>
                  <dd className="text-sm text-gray-900 font-mono">{feature.feature_id}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Primary Stage</dt>
                  <dd className="text-sm text-gray-900 capitalize">{feature.stage}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Category</dt>
                  <dd className="text-sm text-gray-900">{feature.category}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Priority</dt>
                  <dd className="text-sm text-gray-900 capitalize">{feature.priority}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Difficulty</dt>
                  <dd className="text-sm text-gray-900 capitalize">{feature.difficulty}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Research Backed</dt>
                  <dd className="text-sm text-gray-900">{feature.research_backed ? 'Yes' : 'No'}</dd>
                </div>
              </dl>
            </div>

            {/* Mechanics */}
            {feature.mechanics && feature.mechanics.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Mechanics</h3>
                <div className="flex flex-wrap gap-2">
                  {feature.mechanics.map((mechanic, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700"
                    >
                      {mechanic}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Impact Areas */}
            {feature.impact_areas && feature.impact_areas.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Impact Areas</h3>
                <div className="flex flex-wrap gap-2">
                  {feature.impact_areas.map((area, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
              <div className="space-y-3">
                <Link
                  href="/matrix"
                  className="w-full btn-primary flex items-center justify-center"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View in Matrix
                </Link>
                <Link
                  href="/features"
                  className="w-full btn-secondary flex items-center justify-center"
                >
                  Browse All Features
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
