'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, Search, Filter, Grid, List, Eye, Star } from 'lucide-react'
import Link from 'next/link'
import SplitView, { useSplitView } from '@/components/SplitView'
import FeatureDetails from '@/components/FeatureDetails'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: 'low' | 'medium' | 'high'
  difficulty: 'low' | 'medium' | 'high'
  research_backed: boolean
  created_at: string
  stage_impacts?: Array<{
    stage: string
    impact_level: 'none' | 'low' | 'medium' | 'high'
  }>
  examples?: Array<{
    company_name: string
    success_story: string
  }>
}

interface FeaturesData {
  features: Feature[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

const STAGE_COLORS = {
  acquisition: 'bg-blue-500',
  activation: 'bg-green-500',
  retention: 'bg-purple-500',
  referral: 'bg-pink-500',
  revenue: 'bg-yellow-500'
}

const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-700',
  medium: 'bg-blue-100 text-blue-700',
  high: 'bg-red-100 text-red-700'
}

const DIFFICULTY_COLORS = {
  low: 'bg-green-100 text-green-700',
  medium: 'bg-yellow-100 text-yellow-700',
  high: 'bg-red-100 text-red-700'
}

export default function FeaturesPage() {
  const [data, setData] = useState<FeaturesData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [stageFilter, setStageFilter] = useState<string>('')
  const { selectedFeatureId, openDetails, closeDetails, isDetailsOpen } = useSplitView()
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [page, setPage] = useState(1)

  useEffect(() => {
    fetchFeatures()
  }, [stageFilter, priorityFilter, page])

  const fetchFeatures = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12'
      })
      
      if (stageFilter) params.append('stage', stageFilter)
      if (priorityFilter) params.append('priority', priorityFilter)
      if (searchTerm) params.append('search', searchTerm)
      
      const response = await fetch(`/api/features?${params}`)
      if (!response.ok) throw new Error('Failed to fetch features')
      
      const result = await response.json()
      setData({
        features: result.data,
        pagination: result.pagination
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load features')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setPage(1)
    fetchFeatures()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neo-cyan mx-auto mb-4"></div>
          <p className="text-gray-600">Loading features...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <button onClick={fetchFeatures} className="btn-primary">
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <SplitView
      detailsComponent={selectedFeatureId ? <FeatureDetails featureId={selectedFeatureId} compact /> : undefined}
      onCloseDetails={closeDetails}
      className="min-h-screen bg-gray-50"
    >
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Home
              </Link>
              <div className="ml-6 h-6 w-px bg-gray-300"></div>
              <h1 className="ml-6 text-xl font-semibold text-gray-900">
                Gamification Features
              </h1>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-neo-cyan text-white' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-neo-cyan text-white' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search features..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="w-full pl-10 pr-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white placeholder-gray-400"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <select
                value={stageFilter}
                onChange={(e) => setStageFilter(e.target.value)}
                className="px-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white"
              >
                <option value="">All Stages</option>
                <option value="acquisition">Acquisition</option>
                <option value="activation">Activation</option>
                <option value="retention">Retention</option>
                <option value="referral">Referral</option>
                <option value="revenue">Revenue</option>
              </select>

              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white"
              >
                <option value="">All Priorities</option>
                <option value="high">High Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="low">Low Priority</option>
              </select>

              <button
                onClick={handleSearch}
                className="btn-primary flex items-center"
              >
                <Filter className="w-4 h-4 mr-2" />
                Apply
              </button>
            </div>
          </div>

          {data && (
            <div className="mt-4 text-sm text-gray-600">
              Showing {data.features?.length || 0} of {data.pagination?.total || 0} features
            </div>
          )}
        </div>

        {/* Features Grid/List */}
        {data && data.features && data.features.length > 0 ? (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {data.features.map((feature) => (
                  <div key={feature.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className={`w-3 h-3 rounded-full ${STAGE_COLORS[feature.stage as keyof typeof STAGE_COLORS]}`}></div>
                        {feature.research_backed && (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {feature.name}
                      </h3>
                      
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {feature.description}
                      </p>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${PRIORITY_COLORS[feature.priority]}`}>
                          {feature.priority}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${DIFFICULTY_COLORS[feature.difficulty]}`}>
                          {feature.difficulty}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                          {feature.category}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500 capitalize">
                          {feature.stage} stage
                        </span>
                        <button
                          onClick={() => openDetails(feature.id)}
                          className="text-neo-cyan hover:text-neo-cyan-dark flex items-center text-sm hover:bg-neo-cyan/10 px-2 py-1 rounded transition-colors"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="divide-y divide-gray-200">
                  {data.features.map((feature) => (
                    <div key={feature.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`w-3 h-3 rounded-full ${STAGE_COLORS[feature.stage as keyof typeof STAGE_COLORS]}`}></div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {feature.name}
                            </h3>
                            {feature.research_backed && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          
                          <p className="text-gray-600 mb-3">
                            {feature.description}
                          </p>
                          
                          <div className="flex flex-wrap gap-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${PRIORITY_COLORS[feature.priority]}`}>
                              {feature.priority}
                            </span>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${DIFFICULTY_COLORS[feature.difficulty]}`}>
                              {feature.difficulty}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                              {feature.category}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                              {feature.stage}
                            </span>
                          </div>
                        </div>
                        
                        <div className="ml-6">
                          <button
                            onClick={() => openDetails(feature.id)}
                            className="btn-primary flex items-center"
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Pagination */}
            {data.pagination && data.pagination.totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <div className="flex gap-2">
                  <button
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                    className="pagination-btn"
                  >
                    Previous
                  </button>

                  {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1).map((pageNum) => (
                    <button
                      key={pageNum}
                      onClick={() => setPage(pageNum)}
                      className={`pagination-btn ${
                        page === pageNum ? 'pagination-btn-active' : ''
                      }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  <button
                    onClick={() => setPage(page + 1)}
                    disabled={page === data.pagination.totalPages}
                    className="pagination-btn"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        ) : data && data.features && data.features.length === 0 ? (
          <div className="bg-gray-800 rounded-lg shadow-sm p-12 text-center border border-gray-700">
            <p className="text-gray-300 text-lg mb-4">No features found</p>
            <p className="text-gray-400">Try adjusting your search or filter criteria</p>
          </div>
        ) : null}
      </div>
      </div>
    </SplitView>
  )
}
