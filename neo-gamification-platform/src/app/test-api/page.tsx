'use client'

import { useState } from 'react'

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testAPI = async (endpoint: string, method: string = 'GET', body?: any) => {
    setLoading(true)
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      }

      if (body) {
        options.body = JSON.stringify(body)
      }

      const response = await fetch(endpoint, options)
      const data = await response.json()
      
      setResult(`${method} ${endpoint}\nStatus: ${response.status}\nResponse:\n${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testFeatures = () => testAPI('/api/features?limit=5')
  
  const testMatrix = () => testAPI('/api/analytics/matrix')
  
  const testStageImpacts = () => testAPI('/api/stage-impacts')
  
  const testExamples = () => testAPI('/api/examples')

  const testCreateFeature = () => testAPI('/api/features', 'POST', {
    feature_id: 'test_feature_api',
    name: 'Test Feature',
    description: 'This is a test feature created via API',
    category: 'Test Category',
    stage: 'activation',
    priority: 'medium',
    difficulty: 'medium',
    research_backed: true,
    justification: 'This is a test feature for API validation'
  })

  const getFirst5Features = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/features?limit=5')
      const result = await response.json()

      if (!result.data || result.data.length === 0) {
        setResult('No features found')
        setLoading(false)
        return
      }

      const featuresInfo = result.data.map((feature, index) =>
        `${index + 1}. ${feature.name}
   ID: ${feature.id}
   Feature ID: ${feature.feature_id}
   Description: ${feature.description}
   Category: ${feature.category}
   Primary Stage: ${feature.stage}
   Priority: ${feature.priority}
   ---`
      ).join('\n')

      setResult(`First 5 Features:\n\n${featuresInfo}`)
    } catch (error) {
      setResult(`Error fetching features: ${error}`)
    }
    setLoading(false)
  }

  const resetStageImpacts = async () => {
    setLoading(true)
    try {
      // Get all features first
      const featuresResponse = await fetch('/api/features?limit=50')
      const featuresResult = await featuresResponse.json()

      if (!featuresResult.data || featuresResult.data.length === 0) {
        setResult('No features found')
        setLoading(false)
        return
      }

      const stages = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
      const resetResults = []

      // Reset all stage impacts to 'none' for all features
      for (const feature of featuresResult.data) {
        for (const stage of stages) {
          const response = await fetch('/api/stage-impacts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              feature_id: feature.id,
              stage: stage,
              impact_level: 'none',
              reasoning: 'Reset to baseline - no impact'
            })
          })

          if (response.ok) {
            resetResults.push(`✓ Reset ${feature.name} - ${stage} to 'none'`)
          } else {
            resetResults.push(`✗ Failed to reset ${feature.name} - ${stage}`)
          }
        }
      }

      setResult(`Database reset complete! All stage impacts set to 'none':\n${resetResults.slice(0, 10).join('\n')}${resetResults.length > 10 ? `\n... and ${resetResults.length - 10} more` : ''}`)
    } catch (error) {
      setResult(`Error resetting stage impacts: ${error}`)
    }
    setLoading(false)
  }

  const testCreateResearchBasedStageImpacts = async () => {
    setLoading(true)
    try {
      // Get all features to add research-based impacts
      const featuresResponse = await fetch('/api/features?limit=50')
      const featuresResult = await featuresResponse.json()

      if (!featuresResult.data || featuresResult.data.length === 0) {
        setResult('No features found to add stage impacts to')
        setLoading(false)
        return
      }

      // Research-based impact mapping for common gamification features
      const impactMapping = {
        // Onboarding & Tutorial Features
        'gamified_onboarding': {
          acquisition: { level: 'high', reasoning: 'Gamified onboarding significantly reduces drop-off rates during initial user experience, proven to increase conversion by 30-50%' },
          activation: { level: 'high', reasoning: 'Interactive tutorials with progress tracking lead to 40% higher feature adoption rates' },
          retention: { level: 'medium', reasoning: 'Good onboarding creates positive first impression, contributing to early retention' },
          referral: { level: 'low', reasoning: 'Minimal direct impact on referrals, though satisfied users may share positive experience' },
          revenue: { level: 'medium', reasoning: 'Better onboarding leads to higher feature utilization and eventual premium conversions' }
        },

        // Points & Rewards Systems
        'points_system': {
          acquisition: { level: 'medium', reasoning: 'Points systems in marketing can increase sign-up rates by 15-25%' },
          activation: { level: 'high', reasoning: 'Immediate point rewards for key actions increase feature engagement by 35-60%' },
          retention: { level: 'high', reasoning: 'Points create habit loops and increase daily active users by 25-40%' },
          referral: { level: 'medium', reasoning: 'Point rewards for referrals can boost referral rates by 20-30%' },
          revenue: { level: 'high', reasoning: 'Points systems drive premium conversions and increase customer lifetime value by 15-25%' }
        },

        // Achievement & Badge Systems
        'achievement_system': {
          acquisition: { level: 'low', reasoning: 'Achievements primarily benefit existing users rather than attracting new ones' },
          activation: { level: 'high', reasoning: 'Achievement unlocks guide users through key features, increasing activation by 30%' },
          retention: { level: 'high', reasoning: 'Achievement hunting creates long-term engagement goals, proven to increase retention by 20-35%' },
          referral: { level: 'medium', reasoning: 'Social achievements and sharing badges can encourage referrals' },
          revenue: { level: 'medium', reasoning: 'Premium achievements and exclusive badges drive subscription upgrades' }
        },

        // Leaderboards & Competition
        'leaderboard': {
          acquisition: { level: 'medium', reasoning: 'Competitive elements in marketing can attract competitive users' },
          activation: { level: 'medium', reasoning: 'Leaderboards motivate initial engagement but may intimidate new users' },
          retention: { level: 'high', reasoning: 'Competition drives daily engagement, increasing retention by 25-45%' },
          referral: { level: 'high', reasoning: 'Users invite friends to compete, boosting referral rates by 40-60%' },
          revenue: { level: 'medium', reasoning: 'Competitive users more likely to pay for advantages or premium features' }
        },

        // Progress Tracking
        'progress_tracking': {
          acquisition: { level: 'low', reasoning: 'Progress tracking is an internal feature with minimal acquisition impact' },
          activation: { level: 'high', reasoning: 'Visual progress indicators increase task completion rates by 40-70%' },
          retention: { level: 'high', reasoning: 'Progress visibility creates commitment and increases retention by 30-50%' },
          referral: { level: 'low', reasoning: 'Personal progress rarely drives referral behavior' },
          revenue: { level: 'medium', reasoning: 'Users seeing progress more likely to invest in premium features' }
        },

        // Social Features
        'social_sharing': {
          acquisition: { level: 'high', reasoning: 'Social sharing is primary acquisition channel, can increase organic growth by 50-100%' },
          activation: { level: 'medium', reasoning: 'Social features encourage initial engagement and feature exploration' },
          retention: { level: 'medium', reasoning: 'Social connections increase stickiness and long-term engagement' },
          referral: { level: 'high', reasoning: 'Direct referral mechanism with proven 60-80% effectiveness' },
          revenue: { level: 'low', reasoning: 'Social features typically don\'t directly drive revenue' }
        },

        // Challenges & Quests
        'daily_challenges': {
          acquisition: { level: 'medium', reasoning: 'Challenge-based marketing campaigns can attract goal-oriented users' },
          activation: { level: 'high', reasoning: 'Structured challenges guide users through key features systematically' },
          retention: { level: 'high', reasoning: 'Daily challenges create habit formation, increasing DAU by 35-55%' },
          referral: { level: 'medium', reasoning: 'Team challenges and competitions encourage friend invitations' },
          revenue: { level: 'medium', reasoning: 'Challenge completion often tied to premium feature usage' }
        }
      }

      const results = []

      // Process each feature
      for (const feature of featuresResult.data) {
        const featureName = feature.name.toLowerCase().replace(/\s+/g, '_')
        const featureId = feature.feature_id?.toLowerCase() || featureName

        // Find matching impact pattern
        let impactPattern = null
        for (const [pattern, impacts] of Object.entries(impactMapping)) {
          if (featureId.includes(pattern) || featureName.includes(pattern) ||
              feature.name.toLowerCase().includes(pattern.replace(/_/g, ' '))) {
            impactPattern = impacts
            break
          }
        }

        // Default pattern for unmatched features based on category/stage
        if (!impactPattern) {
          const primaryStage = feature.stage
          impactPattern = {
            acquisition: { level: primaryStage === 'acquisition' ? 'high' : 'low', reasoning: `Feature primarily targets ${primaryStage} stage` },
            activation: { level: primaryStage === 'activation' ? 'high' : 'medium', reasoning: `Feature primarily targets ${primaryStage} stage` },
            retention: { level: primaryStage === 'retention' ? 'high' : 'medium', reasoning: `Feature primarily targets ${primaryStage} stage` },
            referral: { level: primaryStage === 'referral' ? 'high' : 'low', reasoning: `Feature primarily targets ${primaryStage} stage` },
            revenue: { level: primaryStage === 'revenue' ? 'high' : 'medium', reasoning: `Feature primarily targets ${primaryStage} stage` }
          }
        }

        // Create stage impacts for this feature
        const featureResults = []
        for (const [stage, impact] of Object.entries(impactPattern)) {
          const response = await fetch('/api/stage-impacts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              feature_id: feature.id,
              stage: stage,
              impact_level: impact.level,
              reasoning: impact.reasoning
            })
          })

          const result = await response.json()
          featureResults.push(`${stage}: ${impact.level}`)
        }

        results.push(`${feature.name}: ${featureResults.join(', ')}`)
      }

      setResult(`Created research-based stage impacts:\n${results.join('\n')}`)
    } catch (error) {
      setResult(`Error: ${error}`)
    }
    setLoading(false)
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">API Testing Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <button
          onClick={testFeatures}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/features
        </button>
        
        <button
          onClick={testMatrix}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/analytics/matrix
        </button>
        
        <button
          onClick={testStageImpacts}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/stage-impacts
        </button>
        
        <button
          onClick={testExamples}
          disabled={loading}
          className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/examples
        </button>
        
        <button
          onClick={testCreateFeature}
          disabled={loading}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test POST /api/features
        </button>

        <button
          onClick={getFirst5Features}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          📋 Get First 5 Features
        </button>

        <button
          onClick={resetStageImpacts}
          disabled={loading}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          🔄 Reset All Stage Impacts
        </button>

        <button
          onClick={testCreateResearchBasedStageImpacts}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          📊 Create Research-Based Stage Impacts
        </button>
      </div>

      {loading && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-300 rounded">
          <p className="text-blue-700">Loading...</p>
        </div>
      )}

      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">API Response:</h2>
          <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
            {result}
          </pre>
        </div>
      )}
    </div>
  )
}
