import { getCurrentUserProfile } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  let profile = null
  let authError = null

  try {
    profile = await getCurrentUserProfile()
    console.log('Dashboard - Profile:', profile)
  } catch (error: any) {
    authError = error.message
    console.error('Dashboard - Auth Error:', error)
  }

  // Temporarily disable redirect for debugging
  // if (!profile) {
  //   redirect('/auth/login')
  // }

  const supabase = await createClient()
  
  // Get feature count for dashboard stats
  const { count: featureCount } = await supabase
    .from('features')
    .select('*', { count: 'exact', head: true })

  const { count: userCount } = await supabase
    .from('user_profiles')
    .select('*', { count: 'exact', head: true })

  const handleSignOut = async () => {
    'use server'
    const supabase = await createClient()
    await supabase.auth.signOut()
    redirect('/auth/login')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">N</span>
              </div>
              <h1 className="ml-3 text-xl font-semibold text-gray-900">
                NEO Gamification Platform
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {profile ? (
                <>
                  <div className="text-sm text-gray-600">
                    Welcome, {profile.full_name || profile.email}
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    profile?.role === 'admin'
                      ? 'bg-red-100 text-red-800'
                      : profile?.role === 'editor'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {profile?.role?.toUpperCase() || 'NO ROLE'}
                  </span>
                  <form action={handleSignOut}>
                    <button
                      type="submit"
                      className="text-sm text-gray-600 hover:text-gray-900"
                    >
                      Sign Out
                    </button>
                  </form>
                </>
              ) : (
                <div className="text-sm text-red-600">
                  No authenticated user {authError && `(Error: ${authError})`}
                  <a href="/auth/login" className="ml-2 text-blue-600 hover:text-blue-800">
                    Login
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
            <p className="mt-1 text-sm text-gray-600">
              Welcome to the NEO Bank Gamification Management Platform
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-cyan-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">🎮</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Features
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {featureCount || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">👥</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Users
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {userCount || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">📊</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Your Role
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 capitalize">
                        {profile?.role || 'No role assigned'}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a
                  href="/features"
                  className="p-4 border border-gray-200 rounded-lg hover:border-cyan-300 hover:shadow-md transition-all"
                >
                  <h4 className="font-medium text-gray-900">View Features</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Browse all gamification features
                  </p>
                </a>
                
                <a
                  href="/matrix"
                  className="p-4 border border-gray-200 rounded-lg hover:border-cyan-300 hover:shadow-md transition-all"
                >
                  <h4 className="font-medium text-gray-900">AARRR Matrix</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Strategic impact analysis view
                  </p>
                </a>

                {(profile?.role === 'admin' || profile?.role === 'editor') && (
                  <a
                    href="/admin/features"
                    className="p-4 border border-gray-200 rounded-lg hover:border-cyan-300 hover:shadow-md transition-all"
                  >
                    <h4 className="font-medium text-gray-900">Manage Features</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Add, edit, and organize features
                    </p>
                  </a>
                )}

                {profile?.role === 'admin' && (
                  <a
                    href="/admin/users"
                    className="p-4 border border-gray-200 rounded-lg hover:border-cyan-300 hover:shadow-md transition-all"
                  >
                    <h4 className="font-medium text-gray-900">User Management</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Manage user roles and permissions
                    </p>
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export const metadata = {
  title: 'Dashboard - NEO Bank Gamification Platform',
  description: 'Main dashboard for the NEO Bank gamification management platform',
}
