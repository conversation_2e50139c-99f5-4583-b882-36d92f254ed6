import { getCurrentUserProfile } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import DashboardContent from '@/components/DashboardContent'

export default async function DashboardPage() {
  let profile = null
  let authError = null

  try {
    profile = await getCurrentUserProfile()
    console.log('Dashboard - Profile:', profile)
  } catch (error: any) {
    authError = error.message
    console.error('Dashboard - Auth Error:', error)
  }

  // Temporarily disable redirect for debugging
  // if (!profile) {
  //   redirect('/auth/login')
  // }

  const supabase = await createClient()
  
  // Get feature count for dashboard stats
  const { count: featureCount } = await supabase
    .from('features')
    .select('*', { count: 'exact', head: true })

  const { count: userCount } = await supabase
    .from('user_profiles')
    .select('*', { count: 'exact', head: true })

  const handleSignOut = async () => {
    'use server'
    const supabase = await createClient()
    await supabase.auth.signOut()
    redirect('/auth/login')
  }

  return (
    <DashboardContent
      featureCount={featureCount}
      userCount={userCount}
      profile={profile}
      authError={authError}
    />
  )
}

export const metadata = {
  title: 'Dashboard - NEO Bank Gamification Platform',
  description: 'Main dashboard for the NEO Bank gamification management platform',
}
