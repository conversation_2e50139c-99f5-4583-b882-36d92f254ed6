import { NextRequest } from 'next/server'
import { checkOpenAIConnection } from '@/lib/openai'

// GET /api/test-openai - Test OpenAI connection without authentication
export async function GET(request: NextRequest) {
  try {
    console.log('Testing OpenAI connection...')
    
    // Check if API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return Response.json({
        success: false,
        error: 'OPENAI_API_KEY not configured',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    // Test the connection
    const isHealthy = await checkOpenAIConnection()
    
    return Response.json({
      success: true,
      openai_connected: isHealthy,
      api_key_configured: !!process.env.OPENAI_API_KEY,
      api_key_length: process.env.OPENAI_API_KEY?.length || 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OpenAI test error:', error)
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
