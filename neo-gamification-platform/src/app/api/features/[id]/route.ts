import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserProfile } from '@/lib/auth'
import {
  updateFeatureSchema,
  createErrorResponse,
  createSuccessResponse,
  type FeatureWithDetails
} from '@/lib/validations/api'

// GET /api/features/[id] - Get single feature with full details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple auth check - just verify user is authenticated
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    const resolvedParams = await params
    const supabase = await createClient()

    // Get feature with all related data
    const { data: feature, error } = await supabase
      .from('features_complete')
      .select('*')
      .eq('id', resolvedParams.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return createErrorResponse('Feature not found', 404)
      }
      console.error('Database error:', error)
      return createErrorResponse('Failed to fetch feature', 500)
    }

    return createSuccessResponse(feature)

  } catch (error) {
    console.error('API error:', error)
    return createErrorResponse('Internal server error', 500)
  }
}

// PUT /api/features/[id] - Update feature
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple auth check - just verify user is authenticated
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    const body = await request.json()
    const validatedData = updateFeatureSchema.parse(body)
    const resolvedParams = await params

    const supabase = await createClient()

    // Check if feature exists
    const { data: existingFeature, error: checkError } = await supabase
      .from('features')
      .select('id')
      .eq('id', resolvedParams.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return createErrorResponse('Feature not found', 404)
      }
      console.error('Database error:', checkError)
      return createErrorResponse('Failed to check feature', 500)
    }

    // Update the feature
    const { data: feature, error } = await supabase
      .from('features')
      .update(validatedData)
      .eq('id', resolvedParams.id)
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return createErrorResponse('Failed to update feature', 500)
    }

    return createSuccessResponse(feature, 'Feature updated successfully')

  } catch (error) {
    console.error('API error:', error)
    if (error instanceof Error && error.message.includes('validation')) {
      return createErrorResponse('Invalid feature data', 400)
    }
    return createErrorResponse('Internal server error', 500)
  }
}

// DELETE /api/features/[id] - Delete feature
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple auth check - just verify user is authenticated
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    const resolvedParams = await params
    const supabase = await createClient()

    // Check if feature exists
    const { data: existingFeature, error: checkError } = await supabase
      .from('features')
      .select('id')
      .eq('id', resolvedParams.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return createErrorResponse('Feature not found', 404)
      }
      console.error('Database error:', checkError)
      return createErrorResponse('Failed to check feature', 500)
    }

    // Delete the feature (cascade will handle related records)
    const { error } = await supabase
      .from('features')
      .delete()
      .eq('id', resolvedParams.id)

    if (error) {
      console.error('Database error:', error)
      return createErrorResponse('Failed to delete feature', 500)
    }

    return createSuccessResponse(null, 'Feature deleted successfully')

  } catch (error) {
    console.error('API error:', error)
    return createErrorResponse('Internal server error', 500)
  }
}
