import { NextRequest } from 'next/server'

// Store for progress tracking
const progressStore = new Map<string, {
  current: number
  total: number
  currentFeature: string
  status: 'running' | 'completed' | 'error'
  message?: string
}>()

// GET /api/ai-analysis/progress - Server-Sent Events for real-time progress
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const sessionId = searchParams.get('sessionId')
  
  if (!sessionId) {
    return new Response('Session ID required', { status: 400 })
  }

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()
      
      // Send initial connection message
      controller.enqueue(encoder.encode('data: {"type":"connected"}\n\n'))
      
      // Set up interval to check for progress updates
      const interval = setInterval(() => {
        const progress = progressStore.get(sessionId)
        
        if (progress) {
          const data = JSON.stringify({
            type: 'progress',
            current: progress.current,
            total: progress.total,
            currentFeature: progress.currentFeature,
            status: progress.status,
            message: progress.message,
            percentage: progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0
          })
          
          controller.enqueue(encoder.encode(`data: ${data}\n\n`))
          
          // Clean up if completed or error
          if (progress.status === 'completed' || progress.status === 'error') {
            clearInterval(interval)
            progressStore.delete(sessionId)
            controller.close()
          }
        }
      }, 500) // Check every 500ms
      
      // Clean up on client disconnect
      request.signal.addEventListener('abort', () => {
        clearInterval(interval)
        progressStore.delete(sessionId)
        controller.close()
      })
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  })
}

// Helper functions to update progress
export function updateProgress(sessionId: string, current: number, total: number, currentFeature: string) {
  progressStore.set(sessionId, {
    current,
    total,
    currentFeature,
    status: 'running'
  })
}

export function completeProgress(sessionId: string, message?: string) {
  const existing = progressStore.get(sessionId)
  if (existing) {
    progressStore.set(sessionId, {
      ...existing,
      status: 'completed',
      message
    })
  }
}

export function errorProgress(sessionId: string, message: string) {
  const existing = progressStore.get(sessionId)
  if (existing) {
    progressStore.set(sessionId, {
      ...existing,
      status: 'error',
      message
    })
  }
}
