import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserProfile } from '@/lib/auth'
import { createErrorResponse, createSuccessResponse } from '@/lib/validations/api'
import { analyzeFeatureImpact, analyzeMultipleFeatures, checkOpenAIConnection, type FeatureAnalysisInput } from '@/lib/openai'
import { z } from 'zod'

// Validation schemas
const analyzeFeatureSchema = z.object({
  feature_id: z.string().uuid(),
})

const analyzeBatchSchema = z.object({
  feature_ids: z.array(z.string().uuid()).optional(),
  limit: z.number().min(1).max(50).optional().default(10),
  priority_filter: z.enum(['high', 'medium', 'low']).optional(),
  difficulty_filter: z.enum(['high', 'medium', 'low']).optional(),
})

// GET /api/ai-analysis/health - Check OpenAI connection
export async function GET(request: NextRequest) {
  try {
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    // Allow any authenticated user to run AI analysis for now
    // TODO: Implement proper role-based access control later
    // if (profile.role !== 'admin') {
    //   return createErrorResponse('Admin access required for AI analysis', 403)
    // }

    const isHealthy = await checkOpenAIConnection()
    
    return createSuccessResponse({
      openai_connected: isHealthy,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI Analysis health check error:', error)
    return createErrorResponse('Health check failed', 500)
  }
}

// POST /api/ai-analysis - Analyze features with AI
export async function POST(request: NextRequest) {
  try {
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    // Allow any authenticated user to run AI analysis for now
    // TODO: Implement proper role-based access control later
    // if (profile.role !== 'admin') {
    //   return createErrorResponse('Admin access required for AI analysis', 403)
    // }

    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const mode = searchParams.get('mode') || 'single'

    const supabase = await createClient()

    if (mode === 'single') {
      // Single feature analysis
      const { feature_id } = analyzeFeatureSchema.parse(body)

      // Get feature data
      const { data: feature, error: featureError } = await supabase
        .from('features_complete')
        .select('*')
        .eq('id', feature_id)
        .single()

      if (featureError || !feature) {
        return createErrorResponse('Feature not found', 404)
      }

      // Transform feature data for analysis
      const analysisInput: FeatureAnalysisInput = {
        id: feature.id,
        name: feature.name,
        description: feature.description || '',
        category: feature.category || '',
        stage: feature.stage,
        priority: feature.priority,
        difficulty: feature.difficulty,
        research_backed: feature.research_backed || false,
        justification: feature.justification || undefined,
        examples: Array.isArray(feature.feature_examples) ? feature.feature_examples.map((ex: any) => ({
          company_name: ex.company_name,
          success_story: ex.success_story,
          implementation_description: ex.implementation_description,
          metrics_before: ex.metrics_before,
          metrics_after: ex.metrics_after
        })) : undefined,
        mechanics: Array.isArray(feature.mechanics) ? feature.mechanics : undefined,
        impact_areas: Array.isArray(feature.impact_areas) ? feature.impact_areas : undefined
      }

      // Analyze with AI
      const analysisResult = await analyzeFeatureImpact(analysisInput)

      // Update database with results
      const updatePromises = analysisResult.stage_impacts.map(impact =>
        supabase
          .from('stage_impacts')
          .upsert({
            feature_id: feature.id,
            stage: impact.stage,
            impact_level: impact.impact_level,
            reasoning: impact.reasoning,
            confidence_score: impact.confidence_score,
            relationship_strength: impact.relationship_strength,
            estimated_improvement: impact.estimated_improvement,
            analysis_timestamp: analysisResult.analysis_timestamp,
            model_used: analysisResult.model_used
          }, {
            onConflict: 'feature_id,stage'
          })
      )

      const updateResults = await Promise.all(updatePromises)
      const hasErrors = updateResults.some(result => result.error)

      if (hasErrors) {
        console.error('Database update errors:', updateResults.filter(r => r.error))
        return createErrorResponse('Failed to update some stage impacts', 500)
      }

      return createSuccessResponse({
        feature_id: feature.id,
        feature_name: feature.name,
        analysis_result: analysisResult,
        updated_stages: analysisResult.stage_impacts.length
      }, 'Feature analysis completed successfully')

    } else if (mode === 'batch') {
      // Batch analysis
      const validatedData = analyzeBatchSchema.parse(body)

      // Build query for features to analyze
      let queryBuilder = supabase
        .from('features_complete')
        .select('*')

      if (validatedData.feature_ids && validatedData.feature_ids.length > 0) {
        queryBuilder = queryBuilder.in('id', validatedData.feature_ids)
      } else {
        // Apply filters if no specific IDs provided
        if (validatedData.priority_filter) {
          queryBuilder = queryBuilder.eq('priority', validatedData.priority_filter)
        }
        if (validatedData.difficulty_filter) {
          queryBuilder = queryBuilder.eq('difficulty', validatedData.difficulty_filter)
        }
        queryBuilder = queryBuilder.limit(validatedData.limit)
      }

      const { data: features, error: featuresError } = await queryBuilder

      if (featuresError) {
        return createErrorResponse('Failed to fetch features', 500)
      }

      if (!features || features.length === 0) {
        return createErrorResponse('No features found matching criteria', 404)
      }

      // Transform features for analysis
      const analysisInputs: FeatureAnalysisInput[] = features.map(feature => ({
        id: feature.id,
        name: feature.name,
        description: feature.description || '',
        category: feature.category || '',
        stage: feature.stage,
        priority: feature.priority,
        difficulty: feature.difficulty,
        research_backed: feature.research_backed || false,
        justification: feature.justification || undefined,
        examples: Array.isArray(feature.feature_examples) ? feature.feature_examples.map((ex: any) => ({
          company_name: ex.company_name,
          success_story: ex.success_story,
          implementation_description: ex.implementation_description,
          metrics_before: ex.metrics_before,
          metrics_after: ex.metrics_after
        })) : undefined,
        mechanics: Array.isArray(feature.mechanics) ? feature.mechanics : undefined,
        impact_areas: Array.isArray(feature.impact_areas) ? feature.impact_areas : undefined
      }))

      // Analyze all features
      const analysisResults = await analyzeMultipleFeatures(analysisInputs)

      // Update database with all results
      const allUpdatePromises = analysisResults.flatMap(result =>
        result.stage_impacts.map(impact =>
          supabase
            .from('stage_impacts')
            .upsert({
              feature_id: result.feature_id,
              stage: impact.stage,
              impact_level: impact.impact_level,
              reasoning: impact.reasoning,
              confidence_score: impact.confidence_score,
              relationship_strength: impact.relationship_strength,
              estimated_improvement: impact.estimated_improvement,
              analysis_timestamp: result.analysis_timestamp,
              model_used: result.model_used
            }, {
              onConflict: 'feature_id,stage'
            })
        )
      )

      const allUpdateResults = await Promise.all(allUpdatePromises)
      const successfulUpdates = allUpdateResults.filter(result => !result.error).length
      const failedUpdates = allUpdateResults.filter(result => result.error).length

      return createSuccessResponse({
        analyzed_features: analysisResults.length,
        successful_updates: successfulUpdates,
        failed_updates: failedUpdates,
        analysis_results: analysisResults.map(result => ({
          feature_id: result.feature_id,
          overall_confidence: result.overall_confidence,
          stages_analyzed: result.stage_impacts.length
        }))
      }, `Batch analysis completed: ${analysisResults.length} features analyzed`)

    } else {
      return createErrorResponse('Invalid mode. Use "single" or "batch"', 400)
    }

  } catch (error) {
    console.error('AI Analysis error:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse(`Validation error: ${error.errors.map(e => e.message).join(', ')}`, 400)
    }
    
    return createErrorResponse('AI analysis failed', 500)
  }
}
