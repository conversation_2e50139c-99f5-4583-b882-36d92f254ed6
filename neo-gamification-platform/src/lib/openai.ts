import OpenAI from 'openai'

// Lazy initialization of OpenAI client
let openai: OpenAI | null = null

function getOpenAIClient(): OpenAI {
  if (!openai) {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required')
    }
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }
  return openai
}

// Types for our analysis
export interface FeatureAnalysisInput {
  id: string
  name: string
  description: string
  category: string
  stage: string
  priority: string
  difficulty: string
  research_backed: boolean
  justification?: string
  examples?: Array<{
    company_name: string
    success_story: string
    implementation_description?: string
    metrics_before?: string
    metrics_after?: string
  }>
  mechanics?: string[]
  impact_areas?: string[]
}

export interface StageImpactAnalysis {
  stage: 'acquisition' | 'activation' | 'retention' | 'referral' | 'revenue'
  impact_level: 'none' | 'low' | 'medium' | 'high'
  confidence_score: number // 0-100
  reasoning: string
  relationship_strength: 'weak' | 'moderate' | 'strong'
  estimated_improvement?: string
}

export interface FeatureAnalysisResult {
  feature_id: string
  stage_impacts: StageImpactAnalysis[]
  overall_confidence: number
  analysis_timestamp: string
  model_used: string
}

// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000 // 1 second between requests
let lastRequestTime = 0

// Helper function to enforce rate limiting
async function enforceRateLimit(): Promise<void> {
  const now = Date.now()
  const timeSinceLastRequest = now - lastRequestTime
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const waitTime = RATE_LIMIT_DELAY - timeSinceLastRequest
    await new Promise(resolve => setTimeout(resolve, waitTime))
  }
  
  lastRequestTime = Date.now()
}

// Create the analysis prompt
function createAnalysisPrompt(feature: FeatureAnalysisInput): string {
  const examplesText = feature.examples?.length 
    ? `\n\nReal-world examples:\n${feature.examples.map(ex => 
        `- ${ex.company_name}: ${ex.success_story}${ex.metrics_before && ex.metrics_after 
          ? ` (Metrics: ${ex.metrics_before} → ${ex.metrics_after})` 
          : ''}`
      ).join('\n')}`
    : ''

  const mechanicsText = feature.mechanics?.length 
    ? `\n\nGameplay mechanics: ${feature.mechanics.join(', ')}`
    : ''

  const impactAreasText = feature.impact_areas?.length 
    ? `\n\nImpact areas: ${feature.impact_areas.join(', ')}`
    : ''

  return `You are an expert in gamification and the AARRR framework (Acquisition, Activation, Retention, Referral, Revenue). 

Analyze the following gamification feature and assess its impact on each AARRR stage:

**Feature Details:**
- Name: ${feature.name}
- Description: ${feature.description}
- Category: ${feature.category}
- Primary Stage: ${feature.stage}
- Priority: ${feature.priority}
- Difficulty: ${feature.difficulty}
- Research Backed: ${feature.research_backed}
- Justification: ${feature.justification || 'Not provided'}${examplesText}${mechanicsText}${impactAreasText}

**Analysis Requirements:**
For each AARRR stage (Acquisition, Activation, Retention, Referral, Revenue), provide:

1. **Impact Level**: none, low, medium, or high
2. **Confidence Score**: 0-100 (how confident you are in this assessment)
3. **Relationship Strength**: weak, moderate, or strong (how directly related the feature is to this stage)
4. **Reasoning**: Detailed explanation of why this feature impacts (or doesn't impact) this stage
5. **Estimated Improvement**: If impact is medium/high, provide a realistic percentage improvement estimate

**Evaluation Criteria:**
- **None (0%)**: No meaningful impact on this stage
- **Low (1-10%)**: Minimal, indirect impact
- **Medium (10-30%)**: Moderate, measurable impact
- **High (30%+)**: Significant, direct impact

**Confidence Scoring:**
- 90-100: Very high confidence (clear, direct relationship with strong evidence)
- 70-89: High confidence (logical relationship with some evidence)
- 50-69: Medium confidence (reasonable assumption but limited evidence)
- 30-49: Low confidence (weak relationship or limited data)
- 0-29: Very low confidence (speculative or unclear relationship)

**Relationship Strength:**
- **Strong**: Feature directly and primarily affects this stage
- **Moderate**: Feature has clear but secondary effects on this stage  
- **Weak**: Feature has minimal or indirect effects on this stage

Respond with a JSON object in this exact format:
{
  "stage_impacts": [
    {
      "stage": "acquisition",
      "impact_level": "none|low|medium|high",
      "confidence_score": 0-100,
      "reasoning": "detailed explanation",
      "relationship_strength": "weak|moderate|strong",
      "estimated_improvement": "X%" (only if impact is medium/high)
    },
    // ... repeat for all 5 stages
  ],
  "overall_confidence": 0-100,
  "analysis_notes": "any additional insights or considerations"
}`
}

// Main analysis function
export async function analyzeFeatureImpact(feature: FeatureAnalysisInput): Promise<FeatureAnalysisResult> {
  try {
    // Enforce rate limiting
    await enforceRateLimit()

    const prompt = createAnalysisPrompt(feature)
    
    const completion = await getOpenAIClient().chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert gamification analyst specializing in the AARRR framework. Provide accurate, data-driven assessments with realistic confidence scores."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3, // Lower temperature for more consistent analysis
      max_tokens: 2000,
      response_format: { type: "json_object" }
    })

    const responseContent = completion.choices[0]?.message?.content
    if (!responseContent) {
      throw new Error('No response content from OpenAI')
    }

    // Parse the JSON response
    const analysisData = JSON.parse(responseContent)
    
    // Validate the response structure
    if (!analysisData.stage_impacts || !Array.isArray(analysisData.stage_impacts)) {
      throw new Error('Invalid response structure from OpenAI')
    }

    // Ensure all 5 stages are present
    const requiredStages = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
    const responseStages = analysisData.stage_impacts.map((impact: any) => impact.stage)
    
    for (const stage of requiredStages) {
      if (!responseStages.includes(stage)) {
        throw new Error(`Missing analysis for stage: ${stage}`)
      }
    }

    return {
      feature_id: feature.id,
      stage_impacts: analysisData.stage_impacts,
      overall_confidence: analysisData.overall_confidence || 0,
      analysis_timestamp: new Date().toISOString(),
      model_used: completion.model
    }

  } catch (error) {
    console.error('Error analyzing feature impact:', error)
    
    // Return a fallback result with low confidence
    return {
      feature_id: feature.id,
      stage_impacts: [
        'acquisition', 'activation', 'retention', 'referral', 'revenue'
      ].map(stage => ({
        stage: stage as any,
        impact_level: 'none' as const,
        confidence_score: 0,
        reasoning: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        relationship_strength: 'weak' as const
      })),
      overall_confidence: 0,
      analysis_timestamp: new Date().toISOString(),
      model_used: 'error'
    }
  }
}

// Batch analysis function
export async function analyzeMultipleFeatures(
  features: FeatureAnalysisInput[],
  onProgress?: (completed: number, total: number, currentFeature: string) => void
): Promise<FeatureAnalysisResult[]> {
  const results: FeatureAnalysisResult[] = []
  
  for (let i = 0; i < features.length; i++) {
    const feature = features[i]
    
    if (onProgress) {
      onProgress(i, features.length, feature.name)
    }
    
    try {
      const result = await analyzeFeatureImpact(feature)
      results.push(result)
    } catch (error) {
      console.error(`Failed to analyze feature ${feature.name}:`, error)
      // Continue with next feature even if one fails
    }
  }
  
  if (onProgress) {
    onProgress(features.length, features.length, 'Complete')
  }
  
  return results
}

// Health check function
export async function checkOpenAIConnection(): Promise<boolean> {
  try {
    const response = await getOpenAIClient().chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Hello" }],
      max_tokens: 5
    })
    
    return !!response.choices[0]?.message?.content
  } catch (error) {
    console.error('OpenAI connection check failed:', error)
    return false
  }
}
