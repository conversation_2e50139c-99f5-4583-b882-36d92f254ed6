// Mock OpenAI service for testing when quota is exceeded
import { type FeatureAnalysisInput, type FeatureAnalysisResult, type AnalysisConfig } from './openai'

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock analysis function that simulates realistic AI responses
export async function mockAnalyzeFeatureImpact(
  feature: FeatureAnalysisInput, 
  config: AnalysisConfig
): Promise<FeatureAnalysisResult> {
  // Simulate API call delay
  await delay(1000 + Math.random() * 2000)

  // Generate realistic mock data based on feature characteristics
  const mockImpacts = [
    {
      stage: 'acquisition' as const,
      impact_level: feature.stage === 'acquisition' ? 'high' as const : 'low' as const,
      confidence_score: feature.stage === 'acquisition' ? 85 : 45,
      reasoning: `Mock analysis: ${feature.name} ${feature.stage === 'acquisition' ? 'directly targets' : 'has minimal impact on'} user acquisition through ${feature.category.toLowerCase()} mechanics.`,
      relationship_strength: feature.stage === 'acquisition' ? 'strong' as const : 'weak' as const,
      estimated_improvement: feature.stage === 'acquisition' ? '25%' : undefined
    },
    {
      stage: 'activation' as const,
      impact_level: feature.stage === 'activation' ? 'high' as const : 'medium' as const,
      confidence_score: feature.stage === 'activation' ? 90 : 65,
      reasoning: `Mock analysis: ${feature.name} ${feature.stage === 'activation' ? 'significantly enhances' : 'moderately supports'} user activation through engagement mechanics.`,
      relationship_strength: feature.stage === 'activation' ? 'strong' as const : 'moderate' as const,
      estimated_improvement: feature.stage === 'activation' ? '35%' : '15%'
    },
    {
      stage: 'retention' as const,
      impact_level: feature.stage === 'retention' ? 'high' as const : 'medium' as const,
      confidence_score: feature.stage === 'retention' ? 95 : 70,
      reasoning: `Mock analysis: ${feature.name} ${feature.stage === 'retention' ? 'is specifically designed for' : 'contributes to'} user retention through habit formation.`,
      relationship_strength: feature.stage === 'retention' ? 'strong' as const : 'moderate' as const,
      estimated_improvement: feature.stage === 'retention' ? '40%' : '20%'
    },
    {
      stage: 'referral' as const,
      impact_level: feature.stage === 'referral' ? 'high' as const : 'low' as const,
      confidence_score: feature.stage === 'referral' ? 80 : 40,
      reasoning: `Mock analysis: ${feature.name} ${feature.stage === 'referral' ? 'directly encourages' : 'has limited impact on'} user referrals.`,
      relationship_strength: feature.stage === 'referral' ? 'strong' as const : 'weak' as const,
      estimated_improvement: feature.stage === 'referral' ? '30%' : undefined
    },
    {
      stage: 'revenue' as const,
      impact_level: feature.stage === 'revenue' ? 'high' as const : 'medium' as const,
      confidence_score: feature.stage === 'revenue' ? 85 : 55,
      reasoning: `Mock analysis: ${feature.name} ${feature.stage === 'revenue' ? 'directly drives' : 'indirectly supports'} revenue through increased engagement.`,
      relationship_strength: feature.stage === 'revenue' ? 'strong' as const : 'moderate' as const,
      estimated_improvement: feature.stage === 'revenue' ? '25%' : '10%'
    }
  ]

  // Filter based on configuration
  const filteredImpacts = mockImpacts.filter(impact => {
    // Filter by confidence score
    if (impact.confidence_score < config.minConfidenceScore) return false
    
    // Filter by relationship strength
    const strengthOrder = { weak: 1, moderate: 2, strong: 3 }
    if (strengthOrder[impact.relationship_strength] < strengthOrder[config.minRelationshipStrength]) return false
    
    // Filter by impact level if required
    if (config.requireHighImpact && !['medium', 'high'].includes(impact.impact_level)) return false
    
    return true
  }).slice(0, config.maxRelationshipsPerFeature)

  return {
    feature_id: feature.id,
    stage_impacts: mockImpacts,
    filtered_impacts: filteredImpacts,
    total_analyzed: mockImpacts.length,
    meaningful_relationships: filteredImpacts.length,
    overall_confidence: Math.round(mockImpacts.reduce((sum, impact) => sum + impact.confidence_score, 0) / mockImpacts.length),
    analysis_timestamp: new Date().toISOString(),
    model_used: 'mock-gpt-4.1-mini-demo'
  }
}

// Mock batch analysis
export async function mockAnalyzeMultipleFeatures(
  features: FeatureAnalysisInput[],
  config: AnalysisConfig,
  onProgress?: (completed: number, total: number, currentFeature: string) => void
): Promise<FeatureAnalysisResult[]> {
  const results: FeatureAnalysisResult[] = []
  
  for (let i = 0; i < features.length; i++) {
    const feature = features[i]
    
    if (onProgress) {
      onProgress(i, features.length, feature.name)
    }
    
    const result = await mockAnalyzeFeatureImpact(feature, config)
    results.push(result)
  }
  
  if (onProgress) {
    onProgress(features.length, features.length, 'Complete')
  }
  
  return results
}

// Mock health check
export async function mockCheckOpenAIConnection(): Promise<boolean> {
  await delay(500)
  return true
}
