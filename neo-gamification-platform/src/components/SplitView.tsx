'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { X, Maximize2, Minimize2 } from 'lucide-react'

interface SplitViewProps {
  children: React.ReactNode
  detailsComponent?: React.ReactNode
  detailsId?: string
  onCloseDetails?: () => void
  className?: string
}

export default function SplitView({ 
  children, 
  detailsComponent, 
  detailsId,
  onCloseDetails,
  className = '' 
}: SplitViewProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024) // lg breakpoint
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (detailsComponent) {
        if (e.key === 'Escape') {
          handleCloseDetails()
        } else if (e.key === 'f' && (e.metaKey || e.ctrlKey)) {
          e.preventDefault()
          setIsExpanded(!isExpanded)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [detailsComponent, isExpanded])

  const handleCloseDetails = () => {
    if (onCloseDetails) {
      onCloseDetails()
    } else {
      // Remove the feature parameter from URL
      const params = new URLSearchParams(searchParams.toString())
      params.delete('feature')
      const newUrl = params.toString() ? `?${params.toString()}` : window.location.pathname
      router.push(newUrl)
    }
  }

  const handleExpandToggle = () => {
    setIsExpanded(!isExpanded)
  }

  const showSplitView = detailsComponent && !isMobile
  const showFullModal = detailsComponent && isMobile

  if (showFullModal) {
    // Mobile: Full-screen modal
    return (
      <>
        <div className={className}>
          {children}
        </div>
        
        {/* Full-screen modal for mobile */}
        <div className="fixed inset-0 bg-white z-50 flex flex-col">
          {/* Modal header */}
          <div className="flex items-center justify-between p-4 border-b bg-white">
            <h2 className="text-lg font-semibold">Feature Details</h2>
            <button
              onClick={handleCloseDetails}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {/* Modal content */}
          <div className="flex-1 overflow-y-auto">
            {detailsComponent}
          </div>
        </div>
      </>
    )
  }

  if (showSplitView) {
    // Desktop: Split view
    return (
      <div className={`flex h-screen ${className}`}>
        {/* Main content */}
        <div 
          className={`transition-all duration-300 ease-in-out ${
            isExpanded ? 'w-0 overflow-hidden' : 'flex-1 min-w-0'
          }`}
        >
          {children}
        </div>
        
        {/* Details panel */}
        <div 
          className={`transition-all duration-300 ease-in-out border-l bg-white ${
            isExpanded ? 'w-full' : 'w-1/2 min-w-[400px] max-w-[800px]'
          }`}
        >
          {/* Details header */}
          <div className="flex items-center justify-between p-4 border-b bg-gray-50">
            <h2 className="text-lg font-semibold">Feature Details</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={handleExpandToggle}
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                title={isExpanded ? 'Minimize' : 'Maximize'}
              >
                {isExpanded ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </button>
              <button
                onClick={handleCloseDetails}
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                title="Close (Esc)"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          {/* Details content */}
          <div className="h-full overflow-y-auto pb-16">
            {detailsComponent}
          </div>
        </div>
      </div>
    )
  }

  // No details: Normal view
  return (
    <div className={className}>
      {children}
    </div>
  )
}

// Hook for managing split view state
export function useSplitView() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const openDetails = (featureId: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('feature', featureId)
    router.push(`?${params.toString()}`)
  }
  
  const closeDetails = () => {
    const params = new URLSearchParams(searchParams.toString())
    params.delete('feature')
    const newUrl = params.toString() ? `?${params.toString()}` : window.location.pathname
    router.push(newUrl)
  }
  
  const selectedFeatureId = searchParams.get('feature')
  
  return {
    selectedFeatureId,
    openDetails,
    closeDetails,
    isDetailsOpen: !!selectedFeatureId
  }
}
