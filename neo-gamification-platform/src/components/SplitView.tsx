'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { X, Maximize2, Minimize2, GripVertical } from 'lucide-react'

interface SplitViewProps {
  children: React.ReactNode
  detailsComponent?: React.ReactNode
  detailsId?: string
  onCloseDetails?: () => void
  className?: string
}

export default function SplitView({
  children,
  detailsComponent,
  detailsId,
  onCloseDetails,
  className = ''
}: SplitViewProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [panelWidth, setPanelWidth] = useState(50) // Percentage
  const [isResizing, setIsResizing] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const containerRef = useRef<HTMLDivElement>(null)
  const resizerRef = useRef<HTMLDivElement>(null)

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024) // lg breakpoint
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Resize functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
  }, [])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const newWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

    // Constrain between 20% and 80%
    const constrainedWidth = Math.min(Math.max(newWidth, 20), 80)
    setPanelWidth(100 - constrainedWidth) // Right panel width
  }, [isResizing])

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (detailsComponent) {
        if (e.key === 'Escape') {
          handleCloseDetails()
        } else if (e.key === 'f' && (e.metaKey || e.ctrlKey)) {
          e.preventDefault()
          setIsExpanded(!isExpanded)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [detailsComponent, isExpanded])

  const handleCloseDetails = () => {
    if (onCloseDetails) {
      onCloseDetails()
    } else {
      // Remove the feature parameter from URL
      const params = new URLSearchParams(searchParams.toString())
      params.delete('feature')
      const newUrl = params.toString() ? `?${params.toString()}` : window.location.pathname
      router.push(newUrl)
    }
  }

  const handleExpandToggle = () => {
    setIsExpanded(!isExpanded)
  }

  const showSplitView = detailsComponent && !isMobile
  const showFullModal = detailsComponent && isMobile

  if (showFullModal) {
    // Mobile: Full-screen modal
    return (
      <>
        <div className={className}>
          {children}
        </div>
        
        {/* Full-screen modal for mobile */}
        <div className="fixed inset-0 bg-white z-50 flex flex-col animate-in slide-in-from-right duration-300">
          {/* Modal header */}
          <div className="flex items-center justify-between p-4 border-b bg-white shadow-sm">
            <h2 className="text-lg font-semibold text-gray-900">Feature Details</h2>
            <button
              onClick={handleCloseDetails}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-500 hover:text-gray-700"
              aria-label="Close details"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Modal content */}
          <div className="flex-1 overflow-y-auto bg-gray-50 relative">
            {detailsComponent}

            {/* Floating close button for mobile */}
            <button
              onClick={handleCloseDetails}
              className="fixed bottom-6 right-6 w-12 h-12 bg-neo-cyan text-white rounded-full shadow-lg hover:bg-neo-cyan-dark transition-colors flex items-center justify-center z-10"
              aria-label="Close details"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </>
    )
  }

  if (showSplitView) {
    // Desktop: Split view
    return (
      <div ref={containerRef} className={`flex h-screen ${className}`}>
        {/* Main content */}
        <div
          className={`transition-all duration-300 ease-in-out ${
            isExpanded ? 'w-0 overflow-hidden' : ''
          }`}
          style={!isExpanded ? { width: `${100 - panelWidth}%` } : {}}
        >
          {children}
        </div>

        {/* Resizer */}
        {!isExpanded && (
          <div
            ref={resizerRef}
            className="w-1 bg-gray-300 hover:bg-neo-cyan cursor-col-resize flex items-center justify-center group transition-colors"
            onMouseDown={handleMouseDown}
          >
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <GripVertical className="w-3 h-3 text-gray-600" />
            </div>
          </div>
        )}

        {/* Details panel */}
        <div
          className={`transition-all duration-300 ease-in-out bg-white shadow-xl ${
            isExpanded ? 'w-full' : ''
          }`}
          style={!isExpanded ? { width: `${panelWidth}%` } : {}}
        >
          {/* Details header */}
          <div className="flex items-center justify-between p-4 border-b bg-white shadow-sm">
            <h2 className="text-lg font-semibold text-gray-900">Feature Details</h2>
            <div className="flex items-center gap-1">
              <button
                onClick={handleExpandToggle}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-500 hover:text-gray-700"
                title={isExpanded ? 'Restore' : 'Maximize'}
              >
                {isExpanded ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </button>
              <button
                onClick={handleCloseDetails}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-500 hover:text-gray-700"
                title="Close (Esc)"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Details content */}
          <div className="h-full overflow-y-auto" style={{ height: 'calc(100vh - 73px)' }}>
            {detailsComponent}
          </div>
        </div>
      </div>
    )
  }

  // No details: Normal view
  return (
    <div className={className}>
      {children}
    </div>
  )
}

// Hook for managing split view state
export function useSplitView() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const openDetails = (featureId: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('feature', featureId)
    router.push(`?${params.toString()}`)
  }
  
  const closeDetails = () => {
    const params = new URLSearchParams(searchParams.toString())
    params.delete('feature')
    const newUrl = params.toString() ? `?${params.toString()}` : window.location.pathname
    router.push(newUrl)
  }
  
  const selectedFeatureId = searchParams.get('feature')
  
  return {
    selectedFeatureId,
    openDetails,
    closeDetails,
    isDetailsOpen: !!selectedFeatureId
  }
}
