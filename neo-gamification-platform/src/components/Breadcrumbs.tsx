'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home, Grid, List, Eye, Settings, BarChart3, Brain, FileText } from 'lucide-react'
import { useState, useEffect } from 'react'

interface BreadcrumbItem {
  label: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  current?: boolean
}

interface BreadcrumbsProps {
  className?: string
  showIcons?: boolean
  maxItems?: number
}

export default function Breadcrumbs({ 
  className = '', 
  showIcons = true, 
  maxItems = 5 
}: BreadcrumbsProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [featureName, setFeatureName] = useState<string>('')

  // Fetch feature name if we have a feature ID in the URL
  useEffect(() => {
    const featureId = searchParams.get('feature')
    if (featureId) {
      fetchFeatureName(featureId)
    } else {
      setFeatureName('')
    }
  }, [searchParams])

  const fetchFeatureName = async (featureId: string) => {
    try {
      const response = await fetch(`/api/features/${featureId}`)
      if (response.ok) {
        const result = await response.json()
        setFeatureName(result.data?.name || 'Unknown Feature')
      }
    } catch (error) {
      console.error('Failed to fetch feature name:', error)
      setFeatureName('Feature Details')
    }
  }

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = []
    const pathSegments = pathname.split('/').filter(Boolean)
    const featureId = searchParams.get('feature')

    // Home/Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      href: '/dashboard',
      icon: Home
    })

    // Handle different routes
    if (pathname === '/matrix') {
      breadcrumbs.push({
        label: 'AARRR Matrix',
        href: '/matrix',
        icon: Grid,
        current: !featureId
      })

      if (featureId && featureName) {
        breadcrumbs.push({
          label: featureName,
          href: `/matrix?feature=${featureId}`,
          icon: Eye,
          current: true
        })
      }
    } else if (pathname === '/features') {
      breadcrumbs.push({
        label: 'Features',
        href: '/features',
        icon: List,
        current: !featureId
      })

      if (featureId && featureName) {
        breadcrumbs.push({
          label: featureName,
          href: `/features?feature=${featureId}`,
          icon: Eye,
          current: true
        })
      }
    } else if (pathname.startsWith('/features/')) {
      breadcrumbs.push({
        label: 'Features',
        href: '/features',
        icon: List
      })

      if (pathSegments[1] && pathSegments[1] !== 'new') {
        const featureDetailName = featureName || 'Feature Details'
        breadcrumbs.push({
          label: featureDetailName,
          href: `/features/${pathSegments[1]}`,
          icon: Eye,
          current: pathSegments.length === 2
        })

        if (pathSegments[2] === 'edit') {
          breadcrumbs.push({
            label: 'Edit',
            href: `/features/${pathSegments[1]}/edit`,
            icon: Settings,
            current: true
          })
        }
      } else if (pathSegments[1] === 'new') {
        breadcrumbs.push({
          label: 'New Feature',
          href: '/features/new',
          icon: Settings,
          current: true
        })
      }
    } else if (pathname === '/ai-logs') {
      breadcrumbs.push({
        label: 'AI Analysis Logs',
        href: '/ai-logs',
        icon: FileText,
        current: true
      })
    } else if (pathname === '/test-api') {
      breadcrumbs.push({
        label: 'API Testing',
        href: '/test-api',
        icon: Settings,
        current: true
      })
    } else if (pathname === '/test-auth') {
      breadcrumbs.push({
        label: 'Auth Testing',
        href: '/test-auth',
        icon: Settings,
        current: true
      })
    } else if (pathname.startsWith('/auth')) {
      breadcrumbs.push({
        label: 'Authentication',
        href: '/auth/login',
        icon: Settings,
        current: true
      })
    } else {
      // Generic path handling
      let currentPath = ''
      pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`
        const isLast = index === pathSegments.length - 1
        
        breadcrumbs.push({
          label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
          href: currentPath,
          current: isLast
        })
      })
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Truncate breadcrumbs if they exceed maxItems
  const displayBreadcrumbs = breadcrumbs.length > maxItems 
    ? [
        breadcrumbs[0], // Always show home
        { label: '...', href: '#', icon: undefined }, // Ellipsis
        ...breadcrumbs.slice(-2) // Show last 2 items
      ]
    : breadcrumbs

  if (breadcrumbs.length <= 1) {
    return null // Don't show breadcrumbs if only home
  }

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {displayBreadcrumbs.map((item, index) => {
          const isLast = index === displayBreadcrumbs.length - 1
          const Icon = item.icon

          return (
            <li key={`${item.href}-${index}`} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
              )}
              
              {item.label === '...' ? (
                <span className="text-gray-400 px-2">...</span>
              ) : isLast || item.current ? (
                <span className="flex items-center text-gray-900 font-medium">
                  {showIcons && Icon && (
                    <Icon className="w-4 h-4 mr-1.5 text-gray-600" />
                  )}
                  <span className="truncate max-w-[200px]">{item.label}</span>
                </span>
              ) : (
                <Link
                  href={item.href}
                  className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                >
                  {showIcons && Icon && (
                    <Icon className="w-4 h-4 mr-1.5" />
                  )}
                  <span className="truncate max-w-[150px]">{item.label}</span>
                </Link>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}

// Hook for programmatic breadcrumb management
export function useBreadcrumbs() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const getCurrentPage = () => {
    if (pathname === '/matrix') return 'AARRR Matrix'
    if (pathname === '/features') return 'Features'
    if (pathname === '/ai-logs') return 'AI Logs'
    if (pathname === '/dashboard') return 'Dashboard'
    return 'Page'
  }

  const isInSplitView = () => {
    return !!searchParams.get('feature')
  }

  return {
    currentPage: getCurrentPage(),
    isInSplitView: isInSplitView(),
    featureId: searchParams.get('feature')
  }
}
