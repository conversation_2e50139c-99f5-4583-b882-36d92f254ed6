'use client'

import { useState, useEffect } from 'react'
import { ExternalLink, Edit, Trash2, Calendar, User, Tag, Target, Zap, BarChart3 } from 'lucide-react'
import Link from 'next/link'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: string
  difficulty: string
  research_backed: boolean
  justification?: string
  created_at: string
  updated_at: string
  examples?: Array<{
    company_name: string
    success_story: string
    implementation_description?: string
    metrics_before?: string
    metrics_after?: string
  }>
  stage_impacts?: Array<{
    stage: string
    impact_level: string
    reasoning: string
    confidence_score?: number
    relationship_strength?: string
    estimated_improvement?: string
  }>
}

interface FeatureDetailsProps {
  featureId: string
  className?: string
  showActions?: boolean
  compact?: boolean
}

export default function FeatureDetails({ 
  featureId, 
  className = '', 
  showActions = true,
  compact = false 
}: FeatureDetailsProps) {
  const [feature, setFeature] = useState<Feature | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchFeature()
  }, [featureId])

  const fetchFeature = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/features/${featureId}`)
      if (!response.ok) throw new Error('Failed to fetch feature')
      
      const result = await response.json()
      setFeature(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load feature')
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'high': return 'bg-purple-100 text-purple-800'
      case 'medium': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-teal-100 text-teal-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getImpactColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-300'
    }
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'acquisition': return '🎯'
      case 'activation': return '⚡'
      case 'retention': return '🔄'
      case 'referral': return '📢'
      case 'revenue': return '💰'
      default: return '📊'
    }
  }

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    )
  }

  if (error || !feature) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="text-center text-red-600">
          <p>{error || 'Feature not found'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      <div className={`${compact ? 'p-4' : 'p-6'} space-y-6`}>
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <h1 className={`font-bold text-gray-900 ${compact ? 'text-lg' : 'text-2xl'}`}>
              {feature.name}
            </h1>
            {showActions && (
              <div className="flex items-center gap-2">
                <Link
                  href={`/features/${feature.id}/edit`}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                >
                  <Edit className="w-4 h-4" />
                </Link>
                <Link
                  href={`/features/${feature.id}`}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Open full page"
                >
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(feature.priority)}`}>
              {feature.priority} priority
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(feature.difficulty)}`}>
              {feature.difficulty} difficulty
            </span>
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {getStageIcon(feature.stage)} {feature.stage}
            </span>
            {feature.research_backed && (
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                📚 Research Backed
              </span>
            )}
          </div>
        </div>

        {/* Description */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
          <p className="text-gray-700 leading-relaxed">{feature.description}</p>
        </div>

        {/* Justification */}
        {feature.justification && (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Justification</h3>
            <p className="text-gray-700 leading-relaxed">{feature.justification}</p>
          </div>
        )}

        {/* Stage Impacts */}
        {feature.stage_impacts && feature.stage_impacts.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">AARRR Impact Analysis</h3>
            <div className="space-y-3">
              {feature.stage_impacts.map((impact, idx) => (
                <div key={idx} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {getStageIcon(impact.stage)} {impact.stage.charAt(0).toUpperCase() + impact.stage.slice(1)}
                      </span>
                      <div className={`w-3 h-3 rounded-full ${getImpactColor(impact.impact_level)}`}></div>
                      <span className="text-xs text-gray-500 capitalize">{impact.impact_level}</span>
                    </div>
                    {impact.confidence_score && (
                      <span className="text-xs text-gray-500">
                        {impact.confidence_score}% confidence
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{impact.reasoning}</p>
                  {impact.estimated_improvement && (
                    <p className="text-xs text-green-600 mt-1">
                      Estimated improvement: {impact.estimated_improvement}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Examples */}
        {feature.examples && feature.examples.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">Real-world Examples</h3>
            <div className="space-y-3">
              {feature.examples.map((example, idx) => (
                <div key={idx} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-sm">{example.company_name}</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{example.success_story}</p>
                  {example.implementation_description && (
                    <p className="text-xs text-gray-500 mb-2">{example.implementation_description}</p>
                  )}
                  {(example.metrics_before || example.metrics_after) && (
                    <div className="flex items-center gap-4 text-xs">
                      {example.metrics_before && (
                        <span className="text-red-600">Before: {example.metrics_before}</span>
                      )}
                      {example.metrics_after && (
                        <span className="text-green-600">After: {example.metrics_after}</span>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="border-t pt-4">
          <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
            <div>
              <span className="font-medium">Created:</span>
              <p>{new Date(feature.created_at).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="font-medium">Updated:</span>
              <p>{new Date(feature.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
