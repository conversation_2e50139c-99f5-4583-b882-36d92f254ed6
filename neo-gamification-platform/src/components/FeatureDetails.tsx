'use client'

import { useState, useEffect } from 'react'
import { ExternalLink, Edit, Trash2, Calendar, User, Tag, Target, Zap, BarChart3 } from 'lucide-react'
import Link from 'next/link'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: string
  difficulty: string
  research_backed: boolean
  justification?: string
  created_at: string
  updated_at: string
  examples?: Array<{
    company_name: string
    success_story: string
    implementation_description?: string
    metrics_before?: string
    metrics_after?: string
  }>
  stage_impacts?: Array<{
    stage: string
    impact_level: string
    reasoning: string
    confidence_score?: number
    relationship_strength?: string
    estimated_improvement?: string
  }>
}

interface FeatureDetailsProps {
  featureId: string
  className?: string
  showActions?: boolean
  compact?: boolean
}

export default function FeatureDetails({ 
  featureId, 
  className = '', 
  showActions = true,
  compact = false 
}: FeatureDetailsProps) {
  const [feature, setFeature] = useState<Feature | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchFeature()
  }, [featureId])

  const fetchFeature = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/features/${featureId}`)
      if (!response.ok) throw new Error('Failed to fetch feature')
      
      const result = await response.json()
      setFeature(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load feature')
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'high': return 'bg-purple-100 text-purple-800'
      case 'medium': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-teal-100 text-teal-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getImpactColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-300'
    }
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'acquisition': return '🎯'
      case 'activation': return '⚡'
      case 'retention': return '🔄'
      case 'referral': return '📢'
      case 'revenue': return '💰'
      default: return '📊'
    }
  }

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    )
  }

  if (error || !feature) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="text-center text-red-600">
          <p>{error || 'Feature not found'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white ${className}`}>
      <div className={`${compact ? 'p-6' : 'p-8'} space-y-8`}>
        {/* Header */}
        <div className="space-y-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className={`font-bold text-gray-900 leading-tight ${compact ? 'text-xl' : 'text-3xl'}`}>
                {feature.name}
              </h1>
              <p className="text-gray-600 mt-2 text-sm">
                {feature.category} • Created {new Date(feature.created_at).toLocaleDateString()}
              </p>
            </div>
            {showActions && (
              <div className="flex items-center gap-2 ml-4">
                <Link
                  href={`/features/${feature.id}/edit`}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Edit feature"
                >
                  <Edit className="w-4 h-4" />
                </Link>
                <Link
                  href={`/features/${feature.id}`}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Open full page"
                >
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3">
            <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${getPriorityColor(feature.priority)}`}>
              {feature.priority} priority
            </span>
            <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${getDifficultyColor(feature.difficulty)}`}>
              {feature.difficulty} difficulty
            </span>
            <span className="px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              {getStageIcon(feature.stage)} {feature.stage}
            </span>
            {feature.research_backed && (
              <span className="px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                📚 Research Backed
              </span>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
          <p className="text-gray-700 leading-relaxed text-base">{feature.description}</p>
        </div>

        {/* Justification */}
        {feature.justification && (
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Strategic Justification</h3>
            <p className="text-gray-700 leading-relaxed text-base">{feature.justification}</p>
          </div>
        )}

        {/* Stage Impacts */}
        {feature.stage_impacts && feature.stage_impacts.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">AARRR Impact Analysis</h3>
            <div className="grid gap-4">
              {feature.stage_impacts.map((impact, idx) => (
                <div key={idx} className="bg-white border border-gray-200 rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <span className="text-base font-semibold text-gray-900">
                        {getStageIcon(impact.stage)} {impact.stage.charAt(0).toUpperCase() + impact.stage.slice(1)}
                      </span>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getImpactColor(impact.impact_level)}`}></div>
                        <span className="text-sm font-medium text-gray-700 capitalize">{impact.impact_level} impact</span>
                      </div>
                    </div>
                    {impact.confidence_score && (
                      <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {impact.confidence_score}% confidence
                      </span>
                    )}
                  </div>
                  <p className="text-gray-700 leading-relaxed mb-2">{impact.reasoning}</p>
                  {impact.estimated_improvement && (
                    <div className="bg-green-50 border border-green-200 rounded p-3 mt-3">
                      <p className="text-sm text-green-800 font-medium">
                        📈 Estimated improvement: {impact.estimated_improvement}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Examples */}
        {feature.examples && feature.examples.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Real-world Examples</h3>
            <div className="space-y-4">
              {feature.examples.map((example, idx) => (
                <div key={idx} className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-bold text-sm">{example.company_name.charAt(0)}</span>
                    </div>
                    <span className="font-semibold text-lg text-gray-900">{example.company_name}</span>
                  </div>
                  <p className="text-gray-700 leading-relaxed mb-4">{example.success_story}</p>
                  {example.implementation_description && (
                    <div className="bg-white/70 rounded p-3 mb-4">
                      <p className="text-sm text-gray-600 italic">{example.implementation_description}</p>
                    </div>
                  )}
                  {(example.metrics_before || example.metrics_after) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {example.metrics_before && (
                        <div className="bg-red-50 border border-red-200 rounded p-3">
                          <span className="text-sm font-medium text-red-800">📉 Before:</span>
                          <p className="text-sm text-red-700 mt-1">{example.metrics_before}</p>
                        </div>
                      )}
                      {example.metrics_after && (
                        <div className="bg-green-50 border border-green-200 rounded p-3">
                          <span className="text-sm font-medium text-green-800">📈 After:</span>
                          <p className="text-sm text-green-700 mt-1">{example.metrics_after}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Feature Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Created:</span>
                <span className="text-sm text-gray-600">{new Date(feature.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Updated:</span>
                <span className="text-sm text-gray-600">{new Date(feature.updated_at).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Feature ID:</span>
                <span className="text-sm text-gray-600 font-mono">{feature.feature_id}</span>
              </div>
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Primary Stage:</span>
                <span className="text-sm text-gray-600 capitalize">{feature.stage}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
