'use client'

import Link from 'next/link'
import { BarChart3, Users, Target, TrendingUp, Grid, List, Plus, ArrowRight, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import Breadcrumbs from '@/components/Breadcrumbs'

interface DashboardContentProps {
  featureCount: number | null
  userCount: number | null
  profile: any
  authError: string | null
}

export default function DashboardContent({ 
  featureCount, 
  userCount, 
  profile, 
  authError 
}: DashboardContentProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <div className="py-3 border-b border-gray-100">
            <Breadcrumbs />
          </div>
          
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">NEO Gamification Dashboard</h1>
              <p className="text-gray-600">Strategic gamification features organized by AARRR framework</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Auth Status */}
        {authError && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
              <p className="text-yellow-800">Authentication issue: {authError}</p>
            </div>
          </div>
        )}

        {profile && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <p className="text-green-800">Welcome back, {profile.email}!</p>
            </div>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Features</p>
                <p className="text-2xl font-bold text-gray-900">{featureCount || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">{userCount || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">AARRR Stages</p>
                <p className="text-2xl font-bold text-gray-900">5</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Analytics</p>
                <p className="text-2xl font-bold text-gray-900">Live</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Navigation Cards */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Navigation</h2>
              <div className="space-y-4">
                <Link
                  href="/matrix"
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-neo-cyan hover:bg-neo-cyan/5 transition-colors group"
                >
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-neo-cyan/20">
                      <Grid className="w-5 h-5 text-blue-600 group-hover:text-neo-cyan" />
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">AARRR Matrix</p>
                      <p className="text-sm text-gray-600">View feature impact matrix</p>
                    </div>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-neo-cyan" />
                </Link>

                <Link
                  href="/features"
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-neo-cyan hover:bg-neo-cyan/5 transition-colors group"
                >
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg group-hover:bg-neo-cyan/20">
                      <List className="w-5 h-5 text-green-600 group-hover:text-neo-cyan" />
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">Features List</p>
                      <p className="text-sm text-gray-600">Browse all gamification features</p>
                    </div>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-neo-cyan" />
                </Link>

                <Link
                  href="/ai-logs"
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-neo-cyan hover:bg-neo-cyan/5 transition-colors group"
                >
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-neo-cyan/20">
                      <BarChart3 className="w-5 h-5 text-purple-600 group-hover:text-neo-cyan" />
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">AI Analysis Logs</p>
                      <p className="text-sm text-gray-600">Monitor AI operations</p>
                    </div>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-neo-cyan" />
                </Link>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Database connected</p>
                    <p className="text-xs text-gray-600">System operational</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Clock className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Features loaded</p>
                    <p className="text-xs text-gray-600">{featureCount || 0} features available</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Target className="w-4 h-4 text-purple-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">AARRR framework ready</p>
                    <p className="text-xs text-gray-600">All stages configured</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
