#!/bin/bash

echo "🚀 Adding Environment Variables to Vercel..."
echo ""

# Function to add environment variable
add_env_var() {
    local name=$1
    local value=$2
    echo "Adding $name..."
    echo "$value" | vercel env add "$name" production preview development
    echo ""
}

# Add all environment variables
add_env_var "NEXT_PUBLIC_SUPABASE_URL" "https://gmvuilmcfuszykcpinnc.supabase.co"

add_env_var "NEXT_PUBLIC_SUPABASE_ANON_KEY" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdtdnVpbG1jZnVzenlrY3Bpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2Mzk1OTcsImV4cCI6MjA2NzIxNTU5N30.L07aHlHH2E63uz0LusvDihWVn-wUhnwv55Lxeqth5sg"

add_env_var "SUPABASE_SERVICE_ROLE_KEY" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdtdnVpbG1jZnVzenlrY3Bpbm5jIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzOTU5NywiZXhwIjoyMDY3MjE1NTk3fQ.tIWMfuctnY5-3tujY_30RjRD3p4sXBRak2tyXF9d9Tg"

add_env_var "NODE_ENV" "production"

echo "✅ All environment variables added!"
echo "🚀 Now deploying to production..."

# Deploy to production
vercel --prod
